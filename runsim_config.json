{"window": {"maximized": false, "x": 101, "y": 34, "width": 1500, "height": 967}, "base": "apcpu_sys", "block": "udtb/usvp", "case_files": ["E:/doc/python/runsim/dv/apcpu_sys/bin/case_cfg/apcpu_sys_bus_case.cfg", "E:/doc/python/runsim/dv/top/bin/case_cfg/top_bus_case.cfg", "E:/doc/python/runsim/dv/udtb/apcpu_sys/apcpu_sys_clk/bin/apcpu_sys_clk.cfg", "E:/doc/python/runsim/dv/ap_sys/bin/case_cfg/ap_sys_bus_case.cfg", "E:/doc/python/runsim/dv/udtb/usvp/bin/case_cfg/apcpu_subsys_case.cfg", "E:/doc/python/runsim/dv/udtb/top/top_clk/bin/top_clk.cfg", "E:/doc/python/runsim/dv/udtb/usvp/bin/case_cfg/apcpu_top_case .cfg"], "rundir": "apcpu_clk_sel_test_npg_f1_ffg", "other_options": "", "fsdb": false, "vwdb": false, "cl": false, "fsdb_file": "", "dump_sva": false, "cov": false, "upf": false, "sim_only": false, "compile_only": false, "dump_mem": "", "wdd": "", "seed": "", "simarg": "", "cfg_def": "", "post": "sdf=npg_f1_ffg", "regr_file": "", "fm_checked": false, "bp_server": "", "last_command": "runsim -block top -case top_clk_bist_test", "case": "apcpu_clk_sel_test", "bq_server": "QogirS6_dv", "tag": "", "nt": "", "dashboard": ""}