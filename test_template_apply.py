#!/usr/bin/env python3
"""
测试配置模板应用功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_template_apply_simulation():
    """模拟配置模板应用过程"""
    print("=== 模拟配置模板应用过程 ===")
    
    # 模拟模板配置（从模板文件中读取的配置）
    template_config = {
        'rundir': '{case_name}_work',
        'base': 'test_base',
        'block': 'test_block',
        'fsdb': True
    }
    
    # 模拟当前选中的用例名
    current_case_name = 'testcase1'
    
    print(f"模板配置: {template_config}")
    print(f"当前用例: {current_case_name}")
    
    # 模拟apply_template_config的逻辑
    applied_config = {}
    for key, value in template_config.items():
        if key == 'rundir' and '{case_name}' in str(value):
            # 替换占位符
            applied_value = value.replace('{case_name}', current_case_name)
            applied_config[key] = applied_value
            print(f"rundir占位符替换: '{value}' -> '{applied_value}'")
        else:
            applied_config[key] = value
    
    print(f"应用后配置: {applied_config}")
    
    # 验证结果
    expected_rundir = 'testcase1_work'
    actual_rundir = applied_config.get('rundir')
    
    print(f"\n验证结果:")
    print(f"期望rundir: {expected_rundir}")
    print(f"实际rundir: {actual_rundir}")
    print(f"替换正确: {'✓' if actual_rundir == expected_rundir else '✗'}")
    
    # 测试不同用例名
    print(f"\n=== 测试不同用例名 ===")
    test_cases = ['testcase2', 'complex_case_name', 'simple']
    
    for case_name in test_cases:
        rundir_result = template_config['rundir'].replace('{case_name}', case_name)
        expected = f"{case_name}_work"
        print(f"用例: {case_name} -> rundir: {rundir_result} (期望: {expected}) {'✓' if rundir_result == expected else '✗'}")

def test_command_generation():
    """测试命令生成中的占位符替换"""
    print(f"\n=== 测试命令生成中的占位符替换 ===")
    
    from utils.command_generator import CommandGenerator
    
    # 模拟应用模板后的配置（rundir已经替换过占位符）
    config_after_template = {
        'base': 'test_base',
        'block': 'test_block',
        'rundir': 'testcase1_work'  # 已经替换过的rundir
    }
    
    case_name = 'testcase1'
    command = CommandGenerator.generate_command(config_after_template, 'normal', case_name)
    
    print(f"配置: {config_after_template}")
    print(f"用例: {case_name}")
    print(f"生成命令: {command}")
    print(f"包含正确rundir: {'✓' if 'testcase1_work' in command else '✗'}")
    
    # 测试模板中仍有占位符的情况（用户手动输入的占位符）
    config_with_placeholder = {
        'base': 'test_base',
        'block': 'test_block',
        'rundir': '{case_name}_manual'  # 用户手动输入的占位符
    }
    
    command2 = CommandGenerator.generate_command(config_with_placeholder, 'normal', case_name)
    print(f"\n手动占位符配置: {config_with_placeholder}")
    print(f"生成命令: {command2}")
    print(f"包含正确rundir: {'✓' if 'testcase1_manual' in command2 else '✗'}")

if __name__ == "__main__":
    test_template_apply_simulation()
    test_command_generation()
    print("\n测试完成！")
