#!/usr/bin/env python3
"""
测试简化的rundir占位符功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.command_generator import CommandGenerator

def test_rundir_placeholder():
    """测试rundir占位符替换功能"""
    print("=== 测试rundir占位符替换功能 ===")
    
    # 测试用例1：包含{case_name}占位符的rundir
    config1 = {
        "base": "test_base",
        "block": "test_block", 
        "rundir": "{case_name}_work"
    }
    case_name1 = "testcase1"
    
    command1 = CommandGenerator.generate_command(config1, "normal", case_name1)
    print(f"配置: rundir='{config1['rundir']}', case='{case_name1}'")
    print(f"生成命令: {command1}")
    print(f"期望包含: -rundir testcase1_work")
    print(f"实际包含: {'✓' if 'testcase1_work' in command1 else '✗'}")
    print()
    
    # 测试用例2：不同的用例名
    case_name2 = "testcase2"
    command2 = CommandGenerator.generate_command(config1, "normal", case_name2)
    print(f"配置: rundir='{config1['rundir']}', case='{case_name2}'")
    print(f"生成命令: {command2}")
    print(f"期望包含: -rundir testcase2_work")
    print(f"实际包含: {'✓' if 'testcase2_work' in command2 else '✗'}")
    print()
    
    # 测试用例3：不包含占位符的rundir
    config3 = {
        "base": "test_base",
        "block": "test_block",
        "rundir": "fixed_work_dir"
    }
    command3 = CommandGenerator.generate_command(config3, "normal", "testcase3")
    print(f"配置: rundir='{config3['rundir']}', case='testcase3'")
    print(f"生成命令: {command3}")
    print(f"期望包含: -rundir fixed_work_dir")
    print(f"实际包含: {'✓' if 'fixed_work_dir' in command3 else '✗'}")
    print()
    
    # 测试用例4：复杂的占位符模式
    config4 = {
        "base": "test_base",
        "block": "test_block",
        "rundir": "sim_{case_name}_debug"
    }
    case_name4 = "complex_test"
    command4 = CommandGenerator.generate_command(config4, "normal", case_name4)
    print(f"配置: rundir='{config4['rundir']}', case='{case_name4}'")
    print(f"生成命令: {command4}")
    print(f"期望包含: -rundir sim_complex_test_debug")
    print(f"实际包含: {'✓' if 'sim_complex_test_debug' in command4 else '✗'}")
    print()

def test_template_conversion():
    """测试模板转换功能"""
    print("=== 测试模板转换功能 ===")
    
    from models.config_template_model import RundirPlaceholderHandler
    
    # 测试保存时的转换
    test_cases = [
        ("testcase1_work", "testcase1", "{case_name}_work"),
        ("sim_testcase1_debug", "testcase1", "sim_{case_name}_debug"),
        ("testcase1", "testcase1", "{case_name}"),
        ("unrelated_dir", "testcase1", "unrelated_dir"),
    ]
    
    for rundir, case_name, expected in test_cases:
        result = RundirPlaceholderHandler.convert_rundir_to_template(rundir, case_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} rundir='{rundir}', case='{case_name}' -> '{result}' (期望: '{expected}')")

if __name__ == "__main__":
    test_rundir_placeholder()
    test_template_conversion()
    print("\n测试完成！")
