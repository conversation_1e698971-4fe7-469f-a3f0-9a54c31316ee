"""
配置模板数据模型
"""
import os
import json
import re
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal


class RundirPlaceholderHandler:
    """rundir占位符处理工具类"""

    CASE_NAME_PLACEHOLDER = "{case_name}"

    @staticmethod
    def convert_rundir_to_template(rundir, current_case_name):
        """
        将rundir转换为模板格式（保存时使用）
        智能地将用例相关的路径转换为占位符格式

        Args:
            rundir (str): 工作目录路径
            current_case_name (str): 当前用例名称

        Returns:
            str: 转换后的模板rundir
        """
        if not rundir or not current_case_name:
            return rundir

        # 优先处理：如果rundir包含当前用例名，进行精确替换
        if current_case_name in rundir:
            return rundir.replace(current_case_name, RundirPlaceholderHandler.CASE_NAME_PLACEHOLDER)

        # 如果rundir不包含用例名，但看起来像是用例相关的目录
        # 检查是否为简单目录名（不包含路径分隔符）
        if '/' not in rundir and '\\' not in rundir:
            # 检查常见的用例相关后缀模式
            common_suffixes = ['_work', '_test', '_sim', '_run', '_debug', '_tb']

            for suffix in common_suffixes:
                if rundir.endswith(suffix):
                    # 找到匹配的后缀，转换为占位符格式
                    return RundirPlaceholderHandler.CASE_NAME_PLACEHOLDER + suffix

            # 如果rundir就是用例名（完全匹配）
            if rundir == current_case_name:
                return RundirPlaceholderHandler.CASE_NAME_PLACEHOLDER

            # 如果没有明显的后缀，但是简单的目录名，也可能是用例相关的
            # 这种情况下，将整个rundir作为后缀
            # 例如：rundir="custom_dir", case="test1" -> "{case_name}_custom_dir"
            # 但这可能过于激进，暂时保持原样

        # 其他情况（包含路径分隔符或不匹配任何模式）保持原样
        return rundir

class ConfigTemplateModel(QObject):
    """配置模板模型，负责管理配置模板的保存、加载和管理"""
    
    # 定义信号
    templates_changed = pyqtSignal(list)  # 模板列表变更信号
    template_saved = pyqtSignal(dict)     # 模板保存信号
    template_deleted = pyqtSignal(str)    # 模板删除信号
    
    def __init__(self, template_file="runsim_config_templates.json"):
        """
        初始化配置模板模型
        
        Args:
            template_file (str): 模板文件路径
        """
        super().__init__()
        self.template_file = template_file
        self.templates = []
        
        # 定义需要保存到模板的配置字段
        self.template_fields = [
            'base', 'block', 'rundir', 'bq_server', 'other_options',
            'fsdb', 'vwdb', 'cl', 'dump_sva', 'cov', 'upf', 
            'sim_only', 'compile_only', 'dump_mem', 'wdd', 
            'seed', 'simarg', 'cfg_def', 'post', 'fm_checked', 
            'tag', 'nt', 'dashboard'
        ]
    
    def load_templates(self):
        """
        从文件加载配置模板
        
        Returns:
            list: 模板列表
        """
        self.templates = []
        
        try:
            if os.path.exists(self.template_file):
                with open(self.template_file, 'r', encoding='utf-8') as f:
                    templates_data = json.load(f)
                    
                # 确保 templates_data 是列表类型
                if isinstance(templates_data, list):
                    # 按创建时间倒序排序
                    templates_data.sort(key=lambda x: x.get('created_at', ''), reverse=True)
                    
                    # 验证并添加到模板列表
                    for template in templates_data:
                        if self._validate_template(template):
                            self.templates.append(template)
                            
        except Exception as e:
            print(f"加载配置模板失败: {str(e)}")

        self.templates_changed.emit(self.templates)
        return self.templates
    
    def save_templates(self):
        """
        保存模板到文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            with open(self.template_file, 'w', encoding='utf-8') as f:
                json.dump(self.templates, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            print(f"保存配置模板失败: {str(e)}")
            return False
    
    def save_template(self, name, description, config, current_case_name=None):
        """
        保存新的配置模板

        Args:
            name (str): 模板名称
            description (str): 模板描述
            config (dict): 配置数据
            current_case_name (str): 当前用例名称，用于rundir占位符处理

        Returns:
            bool: 保存是否成功
        """
        # 检查模板名称是否已存在
        if self.get_template_by_name(name):
            return False, "模板名称已存在"

        # 提取需要保存的配置字段（只保存非空值）
        template_config = {}
        for field in self.template_fields:
            if field in config:
                value = config[field]
                # 只保存有意义的值
                if self._is_meaningful_value(value):
                    # 对rundir字段进行占位符处理
                    if field == 'rundir' and current_case_name:
                        value = RundirPlaceholderHandler.convert_rundir_to_template(value, current_case_name)
                    template_config[field] = value
        
        # 创建模板对象
        template = {
            'id': self._generate_template_id(),
            'name': name,
            'description': description,
            'config': template_config,
            'created_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'updated_at': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            'usage_count': 0
        }
        
        # 添加到模板列表开头
        self.templates.insert(0, template)
        
        # 保存到文件
        if self.save_templates():
            self.templates_changed.emit(self.templates)
            self.template_saved.emit(template)
            return True, "模板保存成功"
        else:
            # 保存失败，从列表中移除
            self.templates.remove(template)
            return False, "模板保存失败"
    
    def update_template(self, template_id, name, description, config, current_case_name=None):
        """
        更新现有配置模板

        Args:
            template_id (str): 模板ID
            name (str): 模板名称
            description (str): 模板描述
            config (dict): 配置数据
            current_case_name (str): 当前用例名称，用于rundir占位符处理

        Returns:
            tuple: (成功标志, 消息)
        """
        template = self.get_template_by_id(template_id)
        if not template:
            return False, "模板不存在"

        # 检查名称是否与其他模板冲突
        existing_template = self.get_template_by_name(name)
        if existing_template and existing_template['id'] != template_id:
            return False, "模板名称已存在"

        # 提取需要保存的配置字段（只保存非空值）
        template_config = {}
        for field in self.template_fields:
            if field in config:
                value = config[field]
                # 只保存有意义的值
                if self._is_meaningful_value(value):
                    # 对rundir字段进行占位符处理
                    if field == 'rundir' and current_case_name:
                        value = RundirPlaceholderHandler.convert_rundir_to_template(value, current_case_name)
                    template_config[field] = value
        
        # 更新模板
        template['name'] = name
        template['description'] = description
        template['config'] = template_config
        template['updated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 保存到文件
        if self.save_templates():
            self.templates_changed.emit(self.templates)
            return True, "模板更新成功"
        else:
            return False, "模板更新失败"
    
    def delete_template(self, template_id):
        """
        删除配置模板
        
        Args:
            template_id (str): 模板ID
            
        Returns:
            tuple: (成功标志, 消息)
        """
        template = self.get_template_by_id(template_id)
        if not template:
            return False, "模板不存在"
        
        # 从列表中移除
        self.templates.remove(template)
        
        # 保存到文件
        if self.save_templates():
            self.templates_changed.emit(self.templates)
            self.template_deleted.emit(template_id)
            return True, "模板删除成功"
        else:
            # 删除失败，重新添加到列表
            self.templates.append(template)
            return False, "模板删除失败"
    
    def get_template_by_id(self, template_id):
        """
        根据ID获取模板
        
        Args:
            template_id (str): 模板ID
            
        Returns:
            dict: 模板对象，如果不存在返回None
        """
        for template in self.templates:
            if template.get('id') == template_id:
                return template
        return None
    
    def get_template_by_name(self, name):
        """
        根据名称获取模板
        
        Args:
            name (str): 模板名称
            
        Returns:
            dict: 模板对象，如果不存在返回None
        """
        for template in self.templates:
            if template.get('name') == name:
                return template
        return None
    
    def get_templates(self):
        """
        获取所有模板
        
        Returns:
            list: 模板列表
        """
        return self.templates
    
    def increment_usage_count(self, template_id):
        """
        增加模板使用次数
        
        Args:
            template_id (str): 模板ID
        """
        template = self.get_template_by_id(template_id)
        if template:
            template['usage_count'] = template.get('usage_count', 0) + 1
            template['updated_at'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            self.save_templates()
    
    def _validate_template(self, template):
        """
        验证模板数据的有效性
        
        Args:
            template (dict): 模板数据
            
        Returns:
            bool: 是否有效
        """
        required_fields = ['id', 'name', 'config']
        return all(field in template for field in required_fields)
    
    def _is_meaningful_value(self, value):
        """
        判断配置值是否有意义（非空且非默认值）

        Args:
            value: 配置值

        Returns:
            bool: 是否有意义
        """
        # 布尔值：False被认为是有意义的（用户明确设置为False）
        if isinstance(value, bool):
            return True

        # 字符串值：非空且非纯空白字符
        if isinstance(value, str):
            return bool(value.strip())

        # 数字值：非零值
        if isinstance(value, (int, float)):
            return value != 0

        # 列表值：非空列表
        if isinstance(value, list):
            return len(value) > 0

        # 其他类型：非None
        return value is not None

    def _generate_template_id(self):
        """
        生成唯一的模板ID

        Returns:
            str: 模板ID
        """
        import uuid
        return str(uuid.uuid4())
