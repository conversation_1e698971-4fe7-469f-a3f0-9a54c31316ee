# RunSim GUI配置模板rundir字段修复总结

## 问题描述

配置模板功能在处理rundir（工作目录）字段时存在显示问题：

1. **保存模板时**：rundir字段能正确转换为占位符格式（如 `{case_name}_test`）
2. **应用模板时**：rundir字段能正确替换占位符为当前用例名
3. **模板管理界面显示问题**：显示原始占位符而不是实际值，用户看到 `{case_name}_test` 而不是 `apcpu_stress_test_test`

## 修复内容

### 1. 改进获取当前用例名的方法

**文件**: `views/config_template_dialog.py`

**修改**: `get_current_case_name()` 方法

**改进点**:
- 增加了多个获取用例名的来源
- 优先从 `current_config` 获取
- 备选从父窗口的配置面板获取
- 通过主窗口的 `app_controller` 获取最新配置
- 从用例面板获取当前选中的用例
- 增加了兼容性处理

### 2. 修复模板管理界面rundir显示逻辑

**文件**: `views/config_template_dialog.py`

**修改**: `show_config_preview()` 方法

**修复点**:
- 在显示配置预览时，动态替换rundir字段的占位符
- 为包含占位符的rundir字段设置特殊样式（绿色）
- 添加工具提示显示模板原始值、当前用例和实际值
- 当无法获取当前用例名时显示警告信息

## 修复后的行为

### 保存模板
1. 用户在 "apcpu_hello_world" 用例下设置rundir为 "apcpu_hello_world_test"
2. 保存模板时，系统自动将rundir转换为 "{case_name}_test"
3. 模板中存储的是占位符格式

### 应用模板
1. 用户切换到 "apcpu_stress_test" 用例
2. 选择并应用之前保存的模板
3. 系统自动将 "{case_name}_test" 替换为 "apcpu_stress_test_test"
4. rundir字段正确设置为新的值

### 模板管理界面显示
1. 在模板管理界面中，rundir字段显示实际值而不是占位符
2. 例如：显示 "apcpu_stress_test_test" 而不是 "{case_name}_test"
3. 通过工具提示可以查看模板原始值
4. 动态值用绿色显示，便于识别

## 技术实现细节

### 占位符处理逻辑
```python
# 保存时转换
if current_case_name in rundir:
    template_rundir = rundir.replace(current_case_name, "{case_name}")

# 应用时替换
if '{case_name}' in template_rundir and current_case_name:
    actual_rundir = template_rundir.replace('{case_name}', current_case_name)
```

### 获取当前用例名的优先级
1. `current_config['case']` - 对话框传入的配置
2. `parent().case_input.text()` - 父窗口配置面板
3. `app_controller.config_controller.config_model` - 配置模型
4. `app_controller.case_controller.case_panel` - 用例面板选中项

### 显示逻辑增强
- 动态替换占位符显示实际值
- 特殊样式标识动态生成的值
- 工具提示显示详细信息
- 无法获取用例名时的警告提示

## 兼容性

- 保持与现有PyQt5框架的完全兼容
- 支持Windows和Linux跨平台
- 向后兼容现有的模板文件格式
- 不影响其他配置字段的处理逻辑

## 测试验证

创建了测试脚本验证以下功能：
1. rundir占位符的保存和应用逻辑
2. 获取当前用例名的多种方法
3. 完整的保存-应用工作流程
4. 边界情况处理（空值、多选提示等）

## 用户体验改进

1. **直观显示**: 用户在模板管理界面看到的是实际会应用的rundir值
2. **智能提示**: 通过颜色和工具提示区分动态值和静态值
3. **错误提示**: 当无法确定当前用例时给出明确警告
4. **无缝切换**: 在不同用例间切换时，模板预览自动更新

这个修复确保了配置模板功能的rundir字段能够正确地根据当前选择的用例动态调整，解决了用户反馈的显示问题。
