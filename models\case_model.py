"""
用例数据模型
"""
import os
from PyQt5.QtCore import QObject, pyqtSignal
from utils.case_parser import CaseParser

class CaseModel(QObject):
    """用例数据模型，负责管理用例数据"""
    
    # 定义信号
    case_files_changed = pyqtSignal(list)
    case_data_changed = pyqtSignal(list)
    case_selected = pyqtSignal(str)
    
    def __init__(self):
        """初始化用例模型"""
        super().__init__()
        self.case_files = []  # 用例文件路径列表
        self.case_data = []   # 解析后的用例数据列表
        self.selected_case = ""  # 当前选中的用例
    
    def set_case_files(self, case_files):
        """
        设置用例文件列表
        
        Args:
            case_files (list): 用例文件路径列表
            
        Returns:
            list: 用例文件路径列表
        """
        self.case_files = case_files
        self.case_files_changed.emit(self.case_files)
        return self.case_files
    
    def add_case_file(self, case_file):
        """
        添加用例文件
        
        Args:
            case_file (str): 用例文件路径
            
        Returns:
            list: 更新后的用例文件路径列表
        """
        if case_file not in self.case_files:
            self.case_files.append(case_file)
            self.case_files_changed.emit(self.case_files)
        return self.case_files
    
    def remove_case_file(self, case_file):
        """
        移除用例文件
        
        Args:
            case_file (str): 用例文件路径
            
        Returns:
            list: 更新后的用例文件路径列表
        """
        if case_file in self.case_files:
            self.case_files.remove(case_file)
            self.case_files_changed.emit(self.case_files)
        return self.case_files
    
    def clear_case_files(self):
        """
        清空用例文件列表
        
        Returns:
            list: 空列表
        """
        self.case_files = []
        self.case_files_changed.emit(self.case_files)
        return self.case_files
    
    def get_case_files(self):
        """
        获取用例文件列表
        
        Returns:
            list: 用例文件路径列表
        """
        return self.case_files
    
    def parse_case_files(self, cache_manager=None):
        """
        解析用例文件
        
        Args:
            cache_manager (CacheManager, optional): 缓存管理器
            
        Returns:
            list: 解析后的用例数据列表
        """
        self.case_data = []
        
        for case_file in self.case_files:
            if not os.path.exists(case_file):
                self.case_files.remove(case_file)
            # 使用缓存数据或解析新文件
            if cache_manager:
                cached_data = cache_manager.get_cached_data(case_file)
                if cached_data:
                    self.case_data.append(cached_data)
                    continue
            
            # 解析文件
            file_data = CaseParser.parse_single_file(case_file)
            
            # 保存到缓存
            if cache_manager:
                cache_manager.save_cache(case_file, file_data)
                
            self.case_data.append(file_data)
        
        self.case_data_changed.emit(self.case_data)
        return self.case_data
    
    def get_case_data(self):
        """
        获取解析后的用例数据
        
        Returns:
            list: 解析后的用例数据列表
        """
        return self.case_data
    
    def select_case(self, case_name):
        """
        选择用例
        
        Args:
            case_name (str): 用例名称
            
        Returns:
            str: 选中的用例名称
        """
        self.selected_case = case_name
        self.case_selected.emit(self.selected_case)
        return self.selected_case
    
    def get_selected_case(self):
        """
        获取当前选中的用例
        
        Returns:
            str: 选中的用例名称
        """
        return self.selected_case
