"""
配置模板管理对话框
"""
from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, 
    QPushButton, QTextEdit, QListWidget, QListWidgetItem,
    QMessageBox, QSplitter, QGroupBox, QFormLayout,
    QScrollArea, QWidget, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont

class ConfigTemplateDialog(QDialog):
    """配置模板管理对话框"""
    
    # 定义信号
    template_applied = pyqtSignal(dict)  # 应用模板信号
    
    def __init__(self, template_model, current_config=None, parent=None):
        """
        初始化配置模板对话框
        
        Args:
            template_model: 配置模板模型
            current_config: 当前配置（用于保存新模板）
            parent: 父窗口
        """
        super().__init__(parent)
        self.template_model = template_model
        self.current_config = current_config or {}
        self.current_template = None
        
        self.setWindowTitle("配置模板管理")
        self.setModal(True)
        self.resize(800, 600)
        
        self.init_ui()
        self.connect_signals()
        self.load_templates()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：模板列表
        left_widget = self.create_template_list_widget()
        splitter.addWidget(left_widget)
        
        # 右侧：模板详情和操作
        right_widget = self.create_template_detail_widget()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)
        splitter.setStretchFactor(1, 2)
        
        layout.addWidget(splitter)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        self.save_new_btn = QPushButton("保存当前配置为模板")
        self.save_new_btn.clicked.connect(self.save_new_template)
        
        self.apply_btn = QPushButton("应用选中模板")
        self.apply_btn.clicked.connect(self.apply_selected_template)
        self.apply_btn.setEnabled(False)
        
        self.close_btn = QPushButton("关闭")
        self.close_btn.clicked.connect(self.close)
        
        button_layout.addWidget(self.save_new_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.apply_btn)
        button_layout.addWidget(self.close_btn)
        
        layout.addLayout(button_layout)
    
    def create_template_list_widget(self):
        """创建模板列表部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标题
        title_label = QLabel("配置模板列表")
        title_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(title_label)
        
        # 模板列表
        self.template_list = QListWidget()
        self.template_list.currentItemChanged.connect(self.on_template_selected)
        layout.addWidget(self.template_list)
        
        # 列表操作按钮
        list_button_layout = QHBoxLayout()
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.load_templates)
        
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_selected_template)
        self.delete_btn.setEnabled(False)
        
        list_button_layout.addWidget(self.refresh_btn)
        list_button_layout.addWidget(self.delete_btn)
        list_button_layout.addStretch()
        
        layout.addLayout(list_button_layout)
        
        return widget
    
    def create_template_detail_widget(self):
        """创建模板详情部件"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 模板信息组
        info_group = QGroupBox("模板信息")
        info_layout = QFormLayout(info_group)
        
        self.name_input = QLineEdit()
        self.name_input.setPlaceholderText("输入模板名称")
        
        self.description_input = QTextEdit()
        self.description_input.setPlaceholderText("输入模板描述（可选）")
        self.description_input.setMaximumHeight(80)
        
        self.created_label = QLabel("-")
        self.usage_label = QLabel("-")
        
        info_layout.addRow("名称:", self.name_input)
        info_layout.addRow("描述:", self.description_input)
        info_layout.addRow("创建时间:", self.created_label)
        info_layout.addRow("使用次数:", self.usage_label)
        
        layout.addWidget(info_group)
        
        # 配置预览组
        config_group = QGroupBox("配置预览")
        config_layout = QVBoxLayout(config_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setMaximumHeight(300)
        
        self.config_widget = QWidget()
        self.config_layout = QVBoxLayout(self.config_widget)
        scroll_area.setWidget(self.config_widget)
        
        config_layout.addWidget(scroll_area)
        layout.addWidget(config_group)
        
        # 操作按钮
        detail_button_layout = QHBoxLayout()
        
        self.update_btn = QPushButton("更新模板")
        self.update_btn.clicked.connect(self.update_selected_template)
        self.update_btn.setEnabled(False)
        
        detail_button_layout.addWidget(self.update_btn)
        detail_button_layout.addStretch()
        
        layout.addLayout(detail_button_layout)
        
        return widget
    
    def connect_signals(self):
        """连接信号"""
        self.template_model.templates_changed.connect(self.on_templates_changed)
        self.name_input.textChanged.connect(self.on_template_info_changed)
        self.description_input.textChanged.connect(self.on_template_info_changed)
    
    def load_templates(self):
        """加载模板列表"""
        self.template_model.load_templates()
    
    def on_templates_changed(self, templates):
        """处理模板列表变更"""
        self.template_list.clear()
        
        for template in templates:
            item = QListWidgetItem()
            item.setText(f"{template['name']}")
            item.setData(Qt.UserRole, template)
            
            # 设置工具提示
            tooltip = f"名称: {template['name']}\n"
            if template.get('description'):
                tooltip += f"描述: {template['description']}\n"
            tooltip += f"创建时间: {template.get('created_at', '未知')}\n"
            tooltip += f"使用次数: {template.get('usage_count', 0)}"
            item.setToolTip(tooltip)
            
            self.template_list.addItem(item)
    
    def on_template_selected(self, current, previous):
        """处理模板选择"""
        if current:
            template = current.data(Qt.UserRole)
            self.current_template = template
            self.show_template_detail(template)
            self.apply_btn.setEnabled(True)
            self.delete_btn.setEnabled(True)
            self.update_btn.setEnabled(True)
        else:
            self.current_template = None
            self.clear_template_detail()
            self.apply_btn.setEnabled(False)
            self.delete_btn.setEnabled(False)
            self.update_btn.setEnabled(False)
    
    def show_template_detail(self, template):
        """显示模板详情"""
        # 显示基本信息
        self.name_input.setText(template['name'])
        self.description_input.setText(template.get('description', ''))
        self.created_label.setText(template.get('created_at', '未知'))
        self.usage_label.setText(str(template.get('usage_count', 0)))
        
        # 显示配置预览
        self.show_config_preview(template.get('config', {}))

    def clear_layout(self, layout):
        """安全地清除布局中的所有项目"""
        if not layout:
            return

        while layout.count():
            item = layout.takeAt(0)
            if item:
                if item.widget():
                    widget = item.widget()
                    widget.setParent(None)
                    widget.deleteLater()
                elif item.layout():
                    self.clear_layout(item.layout())
                elif item.spacerItem():
                    # 处理弹性空间项
                    pass
    
    def show_config_preview(self, config):
        """显示配置预览"""
        # 清除现有内容
        self.clear_layout(self.config_layout)

        # 配置字段显示名称映射
        field_names = {
            'base': 'BASE参数',
            'block': 'BLOCK路径',
            'rundir': '运行目录',
            'bq_server': '提交服务器',
            'other_options': '其他选项',
            'fsdb': 'FSDB波形',
            'vwdb': 'VWDB波形',
            'cl': 'CL选项',
            'dump_sva': 'Dump SVA断言',
            'cov': '收集覆盖率',
            'upf': 'UPF仿真',
            'sim_only': '仅仿真',
            'compile_only': '仅编译',
            'dump_mem': '内存转储',
            'wdd': '波形时间',
            'seed': '种子号',
            'simarg': '仿真参数',
            'cfg_def': 'CFG定义',
            'post': '后仿参数',
            'fm_checked': 'FM检查',
            'tag': '标签',
            'nt': 'NT参数',
            'dashboard': '看板参数'
        }

        # 获取当前用例名称，用于rundir占位符替换
        current_case_name = self.get_current_case_name()

        # 显示配置项
        for field, value in config.items():
            if field in field_names:
                label_text = field_names[field]

                if isinstance(value, bool):
                    # 布尔值用复选框显示
                    checkbox = QCheckBox(label_text)
                    checkbox.setChecked(value)
                    checkbox.setEnabled(False)
                    self.config_layout.addWidget(checkbox)
                else:
                    # 字符串值用标签显示
                    if value:  # 只显示非空值
                        display_value = value

                        # 对rundir字段进行特殊处理：替换占位符为实际值
                        if field == 'rundir' and '{case_name}' in value and current_case_name:
                            display_value = value.replace('{case_name}', current_case_name)
                            # 添加提示信息，显示这是动态生成的值
                            label = QLabel(f"{label_text}: {display_value}")
                            label.setWordWrap(True)
                            # 设置工具提示，显示原始模板
                            label.setToolTip(f"模板: {value}\n当前用例: {current_case_name}\n实际值: {display_value}")
                            # 设置样式，表明这是动态值
                            label.setStyleSheet("QLabel { color: #4CAF50; }")
                        else:
                            label = QLabel(f"{label_text}: {display_value}")
                            label.setWordWrap(True)

                        self.config_layout.addWidget(label)

        # 如果rundir包含占位符但没有当前用例名，显示警告
        if 'rundir' in config and '{case_name}' in config['rundir'] and not current_case_name:
            warning_label = QLabel("⚠️ 运行目录包含占位符，但无法获取当前用例名称")
            warning_label.setStyleSheet("QLabel { color: #FF9800; font-weight: bold; }")
            warning_label.setWordWrap(True)
            self.config_layout.addWidget(warning_label)

        # 添加弹性空间
        self.config_layout.addStretch()
    
    def clear_template_detail(self):
        """清除模板详情"""
        self.name_input.clear()
        self.description_input.clear()
        self.created_label.setText("-")
        self.usage_label.setText("-")

        # 清除配置预览
        self.clear_layout(self.config_layout)
    
    def on_template_info_changed(self):
        """处理模板信息变更"""
        # 当用户修改模板信息时，启用更新按钮
        if self.current_template:
            current_name = self.name_input.text().strip()
            current_desc = self.description_input.toPlainText().strip()
            
            original_name = self.current_template.get('name', '')
            original_desc = self.current_template.get('description', '')
            
            # 检查是否有变更
            has_changes = (current_name != original_name or current_desc != original_desc)
            self.update_btn.setEnabled(has_changes and bool(current_name))
    
    def save_new_template(self):
        """保存新模板"""
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入模板名称")
            return

        description = self.description_input.toPlainText().strip()

        # 获取当前用例名称用于rundir占位符处理
        current_case_name = self.get_current_case_name()

        success, message = self.template_model.save_template(name, description, self.current_config, current_case_name)
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.clear_template_detail()
        else:
            QMessageBox.warning(self, "错误", message)
    
    def update_selected_template(self):
        """更新选中的模板"""
        if not self.current_template:
            return
        
        name = self.name_input.text().strip()
        if not name:
            QMessageBox.warning(self, "警告", "请输入模板名称")
            return
        
        description = self.description_input.toPlainText().strip()

        # 获取当前用例名称用于rundir占位符处理
        current_case_name = self.get_current_case_name()

        success, message = self.template_model.update_template(
            self.current_template['id'], name, description, self.current_config, current_case_name
        )
        
        if success:
            QMessageBox.information(self, "成功", message)
        else:
            QMessageBox.warning(self, "错误", message)

    def get_current_case_name(self):
        """
        获取当前用例名称，从多个来源尝试获取最新的用例名

        Returns:
            str: 当前用例名称，如果无法获取则返回None
        """
        # 方法1：从current_config中获取case字段
        case_name = self.current_config.get('case', '')
        if case_name and not case_name.startswith("已选择"):
            case_name = case_name.strip()
            if case_name:
                return case_name

        # 方法2：尝试从父窗口的配置面板获取
        try:
            if self.parent() and hasattr(self.parent(), 'case_input'):
                case_input_text = self.parent().case_input.text().strip()
                if case_input_text and not case_input_text.startswith("已选择"):
                    return case_input_text
        except Exception:
            pass

        # 方法3：尝试从主窗口的app_controller获取
        try:
            # 查找主窗口
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'app_controller'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'app_controller'):
                app_controller = main_window.app_controller

                # 从配置控制器获取
                if hasattr(app_controller, 'config_controller') and hasattr(app_controller.config_controller, 'config_model'):
                    current_config = app_controller.config_controller.config_model.get_config()
                    case_name = current_config.get('case', '').strip()
                    if case_name and not case_name.startswith("已选择"):
                        return case_name

                # 从用例控制器获取当前选中的用例
                if hasattr(app_controller, 'case_controller') and hasattr(app_controller.case_controller, 'case_panel'):
                    case_panel = app_controller.case_controller.case_panel
                    if hasattr(case_panel, 'get_current_selected_case'):
                        selected_case = case_panel.get_current_selected_case()
                        if selected_case:
                            return selected_case
        except Exception:
            pass

        # 方法4：直接从主窗口的控制器获取（兼容性处理）
        try:
            # 查找主窗口
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'config_controller'):
                main_window = main_window.parent()

            if main_window:
                # 尝试从配置控制器获取
                if hasattr(main_window, 'config_controller') and hasattr(main_window.config_controller, 'config_model'):
                    current_config = main_window.config_controller.config_model.get_config()
                    case_name = current_config.get('case', '').strip()
                    if case_name and not case_name.startswith("已选择"):
                        return case_name

                # 尝试从用例控制器获取
                if hasattr(main_window, 'case_controller') and hasattr(main_window.case_controller, 'case_panel'):
                    case_panel = main_window.case_controller.case_panel
                    if hasattr(case_panel, 'get_current_selected_case'):
                        selected_case = case_panel.get_current_selected_case()
                        if selected_case:
                            return selected_case
        except Exception:
            pass

        return None

    def delete_selected_template(self):
        """删除选中的模板"""
        if not self.current_template:
            return
        
        reply = QMessageBox.question(
            self, "确认删除", 
            f"确定要删除模板 '{self.current_template['name']}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            success, message = self.template_model.delete_template(self.current_template['id'])
            
            if success:
                QMessageBox.information(self, "成功", message)
            else:
                QMessageBox.warning(self, "错误", message)
    
    def apply_selected_template(self):
        """应用选中的模板"""
        if not self.current_template:
            return
        
        # 增加使用次数
        self.template_model.increment_usage_count(self.current_template['id'])
        
        # 发射应用模板信号
        self.template_applied.emit(self.current_template['config'])
        
        # 关闭对话框
        self.accept()
