#!/usr/bin/env python3
"""
测试具体的用例场景
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.config_template_model import RundirPlaceholderHandler

def test_specific_scenario():
    """测试具体场景"""
    print("=== 测试具体场景 ===")
    
    # 您的具体场景
    rundir = 'apcpu_hello_world_test'
    case_name = 'apcpu_hello_world'
    
    print(f"rundir: {rundir}")
    print(f"case_name: {case_name}")
    
    # 保存模板时的转换
    template_result = RundirPlaceholderHandler.convert_rundir_to_template(rundir, case_name)
    print(f"转换结果: {template_result}")
    print(f"期望结果: {{case_name}}_test")
    print(f"转换正确: {template_result == '{case_name}_test'}")
    
    # 应用到新用例
    new_case = 'apcpu_stress_test'
    if '{case_name}' in template_result:
        applied_result = template_result.replace('{case_name}', new_case)
        print(f"\n应用到新用例 {new_case}:")
        print(f"应用结果: {applied_result}")
        print(f"期望结果: apcpu_stress_test_test")
        print(f"应用正确: {applied_result == 'apcpu_stress_test_test'}")
    else:
        print(f"\n警告：模板结果中没有占位符，无法正确应用到新用例")

def test_other_cases():
    """测试其他情况"""
    print(f"\n=== 测试其他情况 ===")
    
    test_cases = [
        ('test1_work', 'test1', '{case_name}_work'),
        ('example_sim', 'example', '{case_name}_sim'),
        ('case_debug', 'case', '{case_name}_debug'),
        ('fixed_dir', 'test1', 'fixed_dir'),  # 不应该转换
    ]
    
    for rundir, case_name, expected in test_cases:
        result = RundirPlaceholderHandler.convert_rundir_to_template(rundir, case_name)
        status = '✓' if result == expected else '✗'
        print(f"{status} rundir='{rundir}', case='{case_name}' -> '{result}' (期望: '{expected}')")

if __name__ == "__main__":
    test_specific_scenario()
    test_other_cases()
    print("\n测试完成！")
