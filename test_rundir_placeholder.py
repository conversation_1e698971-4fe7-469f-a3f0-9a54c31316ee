#!/usr/bin/env python3
"""
测试rundir占位符功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.config_template_model import RundirPlaceholderHandler

def test_convert_rundir_to_template():
    """测试将rundir转换为模板格式"""
    print("=== 测试将rundir转换为模板格式 ===")
    
    test_cases = [
        # (rundir, current_case_name, expected_result)
        ("testcase1", "testcase1", "{case_name}"),
        ("testcase1_work", "testcase1", "{case_name}_work"),
        ("testcase1_simulation", "testcase1", "{case_name}_simulation"),
        ("sim_testcase1_work", "testcase1", "sim_{case_name}_work"),
        ("prefix_testcase1", "testcase1", "prefix_{case_name}"),
        ("unrelated_dir", "testcase1", "unrelated_dir"),  # 无法匹配的情况
        ("", "testcase1", ""),  # 空rundir
        ("testcase1_work", "", "testcase1_work"),  # 空用例名
    ]
    
    for rundir, case_name, expected in test_cases:
        result = RundirPlaceholderHandler.convert_rundir_to_template(rundir, case_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} rundir='{rundir}', case='{case_name}' -> '{result}' (期望: '{expected}')")

def test_convert_template_to_rundir():
    """测试将模板rundir转换为实际rundir"""
    print("\n=== 测试将模板rundir转换为实际rundir ===")
    
    test_cases = [
        # (template_rundir, target_case_name, expected_result)
        ("{case_name}", "testcase2", "testcase2"),
        ("{case_name}_work", "testcase2", "testcase2_work"),
        ("{case_name}_simulation", "testcase2", "testcase2_simulation"),
        ("sim_{case_name}_work", "testcase2", "sim_testcase2_work"),
        ("prefix_{case_name}", "testcase2", "prefix_testcase2"),
        ("no_placeholder", "testcase2", "no_placeholder"),  # 无占位符的情况
        ("", "testcase2", ""),  # 空模板
        ("{case_name}_work", "", "{case_name}_work"),  # 空用例名
    ]
    
    for template_rundir, case_name, expected in test_cases:
        result = RundirPlaceholderHandler.convert_template_to_rundir(template_rundir, case_name)
        status = "✓" if result == expected else "✗"
        print(f"{status} template='{template_rundir}', case='{case_name}' -> '{result}' (期望: '{expected}')")

def test_round_trip():
    """测试往返转换"""
    print("\n=== 测试往返转换 ===")
    
    test_cases = [
        ("testcase1_work", "testcase1", "testcase2"),
        ("sim_testcase1_debug", "testcase1", "testcase2"),
        ("testcase1", "testcase1", "testcase2"),
    ]
    
    for original_rundir, original_case, new_case in test_cases:
        # 第一步：转换为模板
        template = RundirPlaceholderHandler.convert_rundir_to_template(original_rundir, original_case)
        # 第二步：转换为新用例的rundir
        new_rundir = RundirPlaceholderHandler.convert_template_to_rundir(template, new_case)
        
        print(f"原始: '{original_rundir}' (case: {original_case})")
        print(f"模板: '{template}'")
        print(f"新的: '{new_rundir}' (case: {new_case})")
        print()

if __name__ == "__main__":
    test_convert_rundir_to_template()
    test_convert_template_to_rundir()
    test_round_trip()
    print("\n测试完成！")
