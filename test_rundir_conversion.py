#!/usr/bin/env python3
"""
测试rundir转换逻辑
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.config_template_model import RundirPlaceholderHandler

def test_rundir_conversion():
    """测试rundir转换逻辑"""
    print("=== 测试rundir转换逻辑 ===")
    
    test_cases = [
        # (rundir, case_name, expected_result, description)
        ('apcpu_hello_world_test', 'apcpu_hello_world_test', '{case_name}', '完全匹配用例名'),
        ('testcase1_work', 'testcase1', '{case_name}_work', '用例名+_work后缀'),
        ('some_test_work', 'some_test', '{case_name}_work', '用例名+_work后缀'),
        ('fixed_directory', 'testcase1', 'fixed_directory', '固定目录名，不转换'),
        ('simulation_run', 'testcase1', '{case_name}_run', '以_run结尾，转换'),
        ('debug_sim', 'testcase1', '{case_name}_sim', '以_sim结尾，转换'),
        ('test_case_test', 'test_case', '{case_name}_test', '用例名+_test后缀'),
        ('/absolute/path/work', 'testcase1', '/absolute/path/work', '绝对路径，不转换'),
        ('relative/path/work', 'testcase1', 'relative/path/work', '相对路径，不转换'),
        ('simple_work', 'other_case', '{case_name}_work', '简单名称+_work，转换'),
    ]
    
    for rundir, case_name, expected, description in test_cases:
        result = RundirPlaceholderHandler.convert_rundir_to_template(rundir, case_name)
        status = '✓' if result == expected else '✗'
        print(f'{status} {description}')
        print(f'    rundir="{rundir}", case="{case_name}" -> "{result}" (期望: "{expected}")')
        if result != expected:
            print(f'    ❌ 转换失败！')
        print()

def test_full_workflow():
    """测试完整的工作流程"""
    print("=== 测试完整工作流程 ===")
    
    # 模拟保存模板的场景
    print("1. 保存模板场景：")
    original_rundir = "apcpu_hello_world_test"
    current_case = "apcpu_hello_world_test"
    
    # 保存时转换为模板
    template_rundir = RundirPlaceholderHandler.convert_rundir_to_template(original_rundir, current_case)
    print(f"   原始rundir: {original_rundir}")
    print(f"   当前用例: {current_case}")
    print(f"   保存为模板: {template_rundir}")
    
    # 模拟应用模板的场景
    print("\n2. 应用模板场景：")
    new_case = "another_test_case"
    
    # 应用时替换占位符
    if '{case_name}' in template_rundir:
        applied_rundir = template_rundir.replace('{case_name}', new_case)
    else:
        applied_rundir = template_rundir
    
    print(f"   模板rundir: {template_rundir}")
    print(f"   新用例: {new_case}")
    print(f"   应用后rundir: {applied_rundir}")
    
    # 验证结果
    expected_applied = "another_test_case"
    success = applied_rundir == expected_applied
    print(f"   结果正确: {'✓' if success else '✗'} (期望: {expected_applied})")

if __name__ == "__main__":
    test_rundir_conversion()
    test_full_workflow()
    print("测试完成！")
